/**
 * 排产规划相关类型定义
 */

// 重新导出 OptimizedBatch 类型
export type { OptimizedBatch } from './production-order-creation';

// 排产阶段类型
export type SchedulingPhase = 'pre-scheduling' | 'cutting-optimization' | 'final-confirmation';

// 排产指标
export interface SchedulingMetrics {
  totalDuration: number;        // 总工期(天)
  equipmentUtilization: number; // 设备利用率(%)
  deliveryAchievement: number;  // 交期达成率(%)
  materialUtilization: number;  // 原片利用率(%)
}

// 甘特图数据
export interface GanttChartData {
  tasks: GanttTask[];
  resources: GanttResource[];
  timeRange: {
    start: string;
    end: string;
  };
}

export interface GanttTask {
  id: string;
  name: string;
  start: string;
  end: string;
  duration: number;
  resourceId: string;
  batchId: string;
  workstation: string;
  status: 'planned' | 'in-progress' | 'completed' | 'delayed';
  dependencies?: string[];
}

export interface GanttResource {
  id: string;
  name: string;
  type: 'equipment' | 'workcenter' | 'process_segment';
  capacity: number;
  utilization: number;
  // 工艺段特有属性
  equipmentIds?: string[];
  processStepIds?: string[];
  status?: 'normal' | 'bottleneck' | 'idle';
  // 设备特有属性
  location?: string;
  // 工作中心特有属性
  shiftConfig?: any;
}

// 预排产结果
export interface PreScheduleResult {
  scheduleId: string;
  batches: ScheduledBatch[];
  ganttData: GanttChartData;
  estimatedMetrics: SchedulingMetrics;
  createdAt: string;
  constraints: SchedulingConstraint[];
}

export interface ScheduledBatch extends OptimizedBatch {
  scheduledStartTime: string;
  scheduledEndTime: string;
  assignedResources: AssignedResource[];
  estimatedCost: number;
}

export interface AssignedResource {
  resourceId: string;
  resourceName: string;
  resourceType: 'equipment' | 'workcenter';
  allocatedTime: number;
  utilization: number;
}

export interface SchedulingConstraint {
  type: 'equipment' | 'material' | 'delivery' | 'capacity';
  description: string;
  severity: 'low' | 'medium' | 'high';
  affectedBatches: string[];
}

// 切割数据导出
export interface CuttingExportData {
  exportId: string;
  exportTime: string;
  batchCount: number;
  batches: CuttingBatchData[];
  availableMaterials: AvailableMaterial[];
  constraints: CuttingConstraints;
  metadata: ExportMetadata;
}

export interface CuttingBatchData {
  batchId: string;
  specifications: {
    length: number;
    width: number;
    thickness: number;
    glassType: string;
    color: string;
  };
  quantity: number;
  priority: number;
  deliveryDate: string;
  customerName: string;
}

export interface AvailableMaterial {
  materialId: string;
  dimensions: {
    length: number;
    width: number;
    thickness: number;
  };
  quantity: number;
  cost: number;
  supplier: string;
  location: string;
}

export interface CuttingConstraints {
  maxUtilizationRate: number;
  minPieceSize: {
    length: number;
    width: number;
  };
  cuttingMargin: number;
  wasteThreshold: number;
}

export interface ExportMetadata {
  version: string;
  format: 'excel' | 'json';
  checksum: string;
  exportedBy: string;
}

// 切割结果导入
export interface CuttingImportResult {
  resultId: string;
  importTime: string;
  cuttingPlans: CuttingPlan[];
  materialUsage: MaterialUsage[];
  timeEstimates: TimeEstimate[];
  improvements: CuttingImprovements;
  validation: ValidationResult;
}

export interface CuttingPlan {
  planId: string;
  materialId: string;
  layout: {
    pieces: CuttingPiece[];
    utilization: number;
    wasteArea: number;
    wastePercentage: number;
  };
  cuttingSequence: CuttingStep[];
}

export interface CuttingPiece {
  batchId: string;
  pieceId: string;
  position: {
    x: number;
    y: number;
  };
  dimensions: {
    length: number;
    width: number;
  };
  rotation: number;
}

export interface CuttingStep {
  stepId: string;
  type: 'horizontal' | 'vertical';
  position: number;
  affectedPieces: string[];
}

export interface MaterialUsage {
  materialId: string;
  usedQuantity: number;
  utilization: number;
  cost: number;
  wasteAmount: number;
}

export interface TimeEstimate {
  batchId: string;
  cuttingTime: number;
  setupTime: number;
  totalTime: number;
}

export interface CuttingImprovements {
  materialSaved: number;
  timeSaved: number;
  utilizationImproved: number;
  costSaved: number;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  code: string;
  message: string;
  field?: string;
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  code: string;
  message: string;
  suggestion?: string;
}

// 最终排产结果
export interface FinalScheduleResult {
  scheduleId: string;
  finalBatches: FinalScheduledBatch[];
  finalGanttData: GanttChartData;
  finalMetrics: SchedulingMetrics;
  cuttingIntegration: CuttingIntegrationData;
  createdAt: string;
  approvedBy?: string;
  status: 'draft' | 'approved' | 'released';
}

export interface FinalScheduledBatch extends ScheduledBatch {
  cuttingPlanId: string;
  actualMaterialUsage: MaterialUsage[];
  actualTimeEstimate: TimeEstimate;
  qualityRequirements: QualityRequirement[];
}

export interface CuttingIntegrationData {
  cuttingPlans: CuttingPlan[];
  materialRequirements: MaterialRequirement[];
  equipmentRequirements: EquipmentRequirement[];
}

export interface MaterialRequirement {
  materialId: string;
  requiredQuantity: number;
  availableQuantity: number;
  shortfall: number;
  procurementNeeded: boolean;
}

export interface EquipmentRequirement {
  equipmentId: string;
  requiredTime: number;
  availableTime: number;
  utilization: number;
  conflicts: string[];
}

export interface QualityRequirement {
  checkPoint: string;
  standard: string;
  tolerance: number;
  inspectionMethod: string;
}

// 导出文件格式
export interface ExportFileFormat {
  filename: string;
  format: 'excel' | 'json' | 'csv';
  size: number;
  downloadUrl: string;
  createdAt: string;
}

// 导入状态
export interface ImportStatus {
  status: 'waiting' | 'importing' | 'validating' | 'imported' | 'error';
  progress: number;
  message: string;
  errors?: ValidationError[];
}

// 对比分析数据
export interface ComparisonData {
  materialUsage: ComparisonMetric;
  utilization: ComparisonMetric;
  duration: ComparisonMetric;
  totalDuration: ComparisonMetric;
  cost?: ComparisonMetric;
}

export interface ComparisonMetric {
  estimated: number;
  actual: number;
  improvement: number;
  improvementPercentage: number;
  unit: string;
}

// 排产配置
export interface SchedulingConfiguration {
  optimizationStrategy: 'time' | 'cost' | 'quality' | 'balanced';
  constraints: {
    maxWorkHours: number;
    minBatchSize: number;
    maxBatchSize: number;
    bufferTime: number;
  };
  preferences: {
    prioritizeDelivery: boolean;
    prioritizeEfficiency: boolean;
    allowOvertimeWork: boolean;
    allowWeekendWork: boolean;
  };
}

// 甘特图视图配置（固定为工艺段视图）
export interface GanttViewConfiguration {
  resourceOrganization: 'process_segment';
  timeGranularity: 'hour' | 'day' | 'week';
  showUtilization: boolean;
  showBottlenecks: boolean;
  enableDrillDown: boolean;
}

// 工艺段资源（专门用于甘特图）
export interface ProcessSegmentResource extends GanttResource {
  type: 'process_segment';
  equipmentIds: string[];
  processStepIds: string[];
  status: 'normal' | 'bottleneck' | 'idle';
  workstationGroup: string; // 对应 ProcessStep 中的 workstationGroup
  totalCapacity: number;
  availableCapacity: number;
  currentLoad: number;
}

// 甘特图下钻数据
export interface GanttDrillDownData {
  parentResourceId: string;
  childResources: GanttResource[];
  childTasks: GanttTask[];
  aggregationLevel: 'equipment' | 'process_step';
}
