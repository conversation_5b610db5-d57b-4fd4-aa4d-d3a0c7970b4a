<template>
  <Card>
    <CardHeader class="pb-2">
      <CardTitle class="text-sm font-medium">{{ title }}</CardTitle>
    </CardHeader>
    <CardContent>
      <div class="flex flex-col items-baseline space-x-2">
        <p class="text-2xl font-bold text-gray-800">{{ formatValue(after) }}</p>
        <p class="text-sm text-gray-500 line-through">{{ formatValue(before) }}</p>
      </div>
      <div class="flex items-center text-sm mt-2" :class="improvementClass">
        <component :is="arrowIcon" class="h-4 w-4 mr-1" />
        <span>{{ improvementPercentage }}%</span>
        <span class="ml-1">{{ improvementText }}</span>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowUp, ArrowDown, Minus } from 'lucide-vue-next';

interface Props {
  title: string;
  before: number;
  after: number;
  unit?: string;
  invertColors?: boolean; // 为 true 时，下降是好的 (例如成本, 工时)
  isCurrency?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  unit: '',
  invertColors: false,
  isCurrency: false,
});

const difference = computed(() => props.after - props.before);
const percentageChange = computed(() => {
  if (props.before === 0) return props.after > 0 ? 100 : 0;
  return (difference.value / props.before) * 100;
});

const improvementPercentage = computed(() => {
  return Math.abs(percentageChange.value).toFixed(1);
});

const isImprovement = computed(() => {
  if (difference.value === 0) return null; // 无变化
  return props.invertColors ? difference.value < 0 : difference.value > 0;
});

const improvementClass = computed(() => {
  if (isImprovement.value === true) return 'text-green-600';
  if (isImprovement.value === false) return 'text-red-600';
  return 'text-gray-500';
});

const arrowIcon = computed(() => {
  if (difference.value === 0) return Minus;
  return percentageChange.value > 0 ? ArrowUp : ArrowDown;
});

const improvementText = computed(() => {
  if (isImprovement.value === true) return '优化';
  if (isImprovement.value === false) return '劣化';
  return '无变化';
});

const formatValue = (value: number) => {
  const options = props.isCurrency ? { style: 'currency', currency: 'CNY' } : {};
  return `${value.toLocaleString('zh-CN', options)}${props.unit}`;
};
</script>
