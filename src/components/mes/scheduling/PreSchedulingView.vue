<template>
  <div class="h-full flex flex-col">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex-1 flex items-center justify-center">
      <div class="text-center space-y-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <div class="space-y-2">
          <div class="text-lg font-medium text-gray-900">正在生成预排产方案...</div>
          <div class="text-sm text-gray-500">{{ loadingMessage }}</div>
          <div class="w-64 h-2 bg-gray-200 rounded-full overflow-hidden mx-auto">
            <div 
              class="h-full bg-blue-500 transition-all duration-1000 ease-out"
              :style="{ width: `${loadingProgress}%` }"
            />
          </div>
          <div class="text-xs text-gray-400">{{ loadingProgress }}% 完成</div>
        </div>
      </div>
    </div>
    
    <!-- 预排产结果 -->
    <div v-else-if="preSchedule" class="flex-1 flex flex-col">
      <!-- 指标概览 -->
      <div class="p-4 border-b bg-gray-50">
        <div class="grid grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">
              {{ preSchedule.estimatedMetrics.totalDuration.toFixed(1) }}
            </div>
            <div class="text-sm text-gray-600">预计工期(天)</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">
              {{ preSchedule.estimatedMetrics.equipmentUtilization.toFixed(0) }}%
            </div>
            <div class="text-sm text-gray-600">设备利用率</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-orange-600">
              {{ preSchedule.estimatedMetrics.deliveryAchievement.toFixed(0) }}%
            </div>
            <div class="text-sm text-gray-600">交期达成率</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">
              {{ preSchedule.estimatedMetrics.materialUtilization.toFixed(0) }}%
            </div>
            <div class="text-sm text-gray-600">原片利用率(估算)</div>
          </div>
        </div>
      </div>
      
      <!-- 标签页 -->
      <div class="border-b">
        <nav class="flex space-x-8 px-4">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            class="py-3 px-1 border-b-2 font-medium text-sm transition-colors"
            :class="activeTab === tab.id 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          >
            <component :is="tab.icon" class="h-4 w-4 inline mr-2" />
            {{ tab.name }}
          </button>
        </nav>
      </div>
      
      <!-- 标签页内容 -->
      <div class="flex-1 overflow-hidden">
        <!-- 甘特图视图 -->
        <div v-if="activeTab === 'gantt'" class="h-full flex flex-col">
          <!-- 甘特图控制栏 -->
          <div class="p-4 border-b bg-gray-50 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <h4 class="font-medium text-gray-900">工艺段甘特图</h4>
            </div>
            <div class="text-sm text-gray-500">
              按工艺段组织，支持下钻查看设备详情
            </div>
          </div>

          <!-- 甘特图内容 -->
          <div class="flex-1 p-4">
            <ProcessSegmentGanttChart
              :scheduled-batches="preSchedule.batches"
              :enable-drill-down="true"
              @task-click="handleTaskClick"
            />
          </div>
        </div>
        
        <!-- 批次列表视图 -->
        <div v-else-if="activeTab === 'batches'" class="h-full overflow-y-auto p-4">
          <div class="space-y-3">
            <ScheduledBatchCard
              v-for="batch in preSchedule.batches"
              :key="batch.id"
              :batch="batch"
              @view-details="handleBatchDetails"
            />
          </div>
        </div>
        
        <!-- 资源视图 -->
        <div v-else-if="activeTab === 'resources'" class="h-full overflow-y-auto p-4">
          <ResourceUtilizationView
            :resources="preSchedule.ganttData.resources"
            :time-range="preSchedule.ganttData.timeRange"
          />
        </div>
        
        <!-- 约束分析 -->
        <div v-else-if="activeTab === 'constraints'" class="h-full overflow-y-auto p-4">
          <ConstraintAnalysisView
            :constraints="preSchedule.constraints"
            :batches="preSchedule.batches"
          />
        </div>
      </div>
      
      <!-- 操作栏 -->
      <div class="p-4 border-t bg-gray-50 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-600">
            排产方案ID: {{ preSchedule.scheduleId }}
          </div>
          <div class="text-sm text-gray-600">
            生成时间: {{ formatDateTime(preSchedule.createdAt) }}
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <Button variant="outline" @click="handleExportSchedule">
            <Download class="h-4 w-4 mr-2" />
            导出方案
          </Button>
          <Button variant="outline" @click="handleRegenerateSchedule">
            <RefreshCw class="h-4 w-4 mr-2" />
            重新生成
          </Button>
          <Button @click="handleConfirmSchedule">
            <Check class="h-4 w-4 mr-2" />
            确认方案
          </Button>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="flex-1 flex items-center justify-center">
      <div class="text-center space-y-4">
        <Calendar class="h-16 w-16 text-gray-300 mx-auto" />
        <div class="space-y-2">
          <div class="text-lg font-medium text-gray-900">准备开始预排产</div>
          <div class="text-sm text-gray-500">请先选择批次并配置排产参数</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { 
  Calendar, 
  BarChart3, 
  Package, 
  Settings, 
  AlertTriangle,
  Download,
  RefreshCw,
  Check
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';

import ProcessSegmentGanttChart from './ProcessSegmentGanttChart.vue';
import ScheduledBatchCard from './ScheduledBatchCard.vue';
import ResourceUtilizationView from './ResourceUtilizationView.vue';
import ConstraintAnalysisView from './ConstraintAnalysisView.vue';
import type { PreScheduleResult } from '@/types/scheduling';

interface Props {
  preSchedule: PreScheduleResult | null;
  loading: boolean;
}

interface Emits {
  (e: 'confirm-schedule'): void;
  (e: 'regenerate-schedule'): void;
  (e: 'export-schedule'): void;
  (e: 'task-click', taskId: string): void;
  (e: 'batch-details', batchId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式状态
const activeTab = ref('gantt');
const loadingProgress = ref(0);
const loadingMessage = ref('初始化排产引擎...');


// 标签页配置
const tabs = [
  { id: 'gantt', name: '工艺段甘特图', icon: BarChart3 },
  { id: 'batches', name: '批次列表', icon: Package },
  { id: 'resources', name: '资源分析', icon: Settings },
  { id: 'constraints', name: '约束分析', icon: AlertTriangle }
];

// 加载进度模拟
let progressInterval: NodeJS.Timeout | null = null;

const startLoadingProgress = () => {
  loadingProgress.value = 0;
  const messages = [
    '初始化排产引擎...',
    '分析批次约束条件...',
    '计算资源需求...',
    '优化排产方案...',
    '生成甘特图数据...',
    '计算预估指标...',
    '完成排产计算...'
  ];
  
  let messageIndex = 0;
  
  progressInterval = setInterval(() => {
    if (loadingProgress.value < 95) {
      loadingProgress.value += Math.random() * 15;
      
      if (loadingProgress.value > messageIndex * 15) {
        loadingMessage.value = messages[Math.min(messageIndex, messages.length - 1)];
        messageIndex++;
      }
    }
  }, 800);
};

const stopLoadingProgress = () => {
  if (progressInterval) {
    clearInterval(progressInterval);
    progressInterval = null;
  }
  loadingProgress.value = 100;
  loadingMessage.value = '排产方案生成完成';
};

// 监听加载状态变化
const unwatchLoading = computed(() => props.loading);
unwatchLoading.value && startLoadingProgress();

// 事件处理
const handleTaskClick = (task: any) => {
  emit('task-click', task.id || task.batchId);
};



const handleBatchDetails = (batchId: string) => {
  emit('batch-details', batchId);
};

const handleConfirmSchedule = () => {
  emit('confirm-schedule');
};

const handleRegenerateSchedule = () => {
  emit('regenerate-schedule');
};

const handleExportSchedule = () => {
  emit('export-schedule');
};

// 工具函数
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 生命周期
onMounted(() => {
  if (props.loading) {
    startLoadingProgress();
  }
});

onUnmounted(() => {
  stopLoadingProgress();
});

// 监听props变化
const unwatchProps = computed(() => {
  if (!props.loading && progressInterval) {
    stopLoadingProgress();
  }
  return props.loading;
});
</script>
